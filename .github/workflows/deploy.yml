name: AI App Deploy (Build, Test, and Ship)

on:
  push:
    branches:
      - main

jobs:
  test_frontend:
    name: Frontend Tests (Vitest)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        working-directory: findu-frontend
        run: npm install

      - name: Run unit tests
        working-directory: findu-frontend
        run: |
          if npm run --silent test -- --version >/dev/null 2>&1; then
            npm run test -- --run
          else
            echo "No test script found. Skipping.";
          fi

  test_backend:
    name: Backend Tests (pytest)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install backend dependencies
        working-directory: findu-backend
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest

      - name: Run backend tests
        working-directory: findu-backend
        run: pytest -q

  build_and_deploy:
    name: Build frontend, then deploy both services via docker-compose on server
    runs-on: ubuntu-latest
    needs: [test_frontend, test_backend]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install frontend dependencies
        working-directory: findu-frontend
        run: npm install

      - name: Build and Export static site
        working-directory: findu-frontend
        run: |
          npm run build
          # Export to `out/` for nginx static serving
          if npm run --silent export >/dev/null 2>&1; then
            npm run export
          else
            npx next export
          fi

      - name: Upload docker-compose definition
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          username: ${{ secrets.DEPLOY_USER }}
          key: ${{ secrets.DEPLOY_SSH_KEY }}
          port: ${{ secrets.DEPLOY_PORT }}
          source: "deploy/docker-compose.yml"
          target: "~/ai-app"
          strip_components: 1

      - name: Upload frontend static 'out' directory
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          username: ${{ secrets.DEPLOY_USER }}
          key: ${{ secrets.DEPLOY_SSH_KEY }}
          port: ${{ secrets.DEPLOY_PORT }}
          source: "findu-frontend/out/**"
          target: "~/ai-app/out"
          strip_components: 1

      - name: Upload backend source code
        uses: appleboy/scp-action@v0.1.7
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          username: ${{ secrets.DEPLOY_USER }}
          key: ${{ secrets.DEPLOY_SSH_KEY }}
          port: ${{ secrets.DEPLOY_PORT }}
          source: "findu-backend/**"
          target: "~/ai-app/findu-backend"
          strip_components: 1

      - name: Launch/Update services with docker compose
        uses: appleboy/ssh-action@v1.0.3
        with:
          host: ${{ secrets.DEPLOY_HOST }}
          username: ${{ secrets.DEPLOY_USER }}
          key: ${{ secrets.DEPLOY_SSH_KEY }}
          port: ${{ secrets.DEPLOY_PORT }}
          script: |
            set -e
            mkdir -p ~/ai-app
            cd ~/ai-app
            # Ensure a clean out dir
            mkdir -p out
            # Bring up services (prefer Docker Compose V2)
            if command -v docker >/dev/null 2>&1 && docker compose version >/dev/null 2>&1; then
              docker compose up -d --build
            else
              docker-compose up -d --build
            fi

