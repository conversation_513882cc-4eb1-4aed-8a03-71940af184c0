name: FindU CI Pipeline

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  frontend_ci:
    frontend_ci:
    name: Frontend CI (Next.js)
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Setup Node.js environment
        uses: actions/setup-node@v4
        with:
          node-version: '18'
      
      - name: Install Frontend dependencies
        run: |
          cd findu-frontend
          npm install
      
      - name: Build Frontend
        run: |
          cd findu-frontend
          npm run build
      
      # ⚠️ 注意: README 中未提及测试命令，此步骤为示例
      - name: Run Frontend tests (Placeholder)
        run: echo "No explicit test command found. Skipping."
  
  backend_ci:
    backend_ci:
    name: Backend CI (FastAPI)
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      
      - name: Setup Python environment
        uses: actions/setup-python@v5
        with:
          python-version: '3.8'
      
      - name: Install Backend dependencies
        run: |
          cd findu-backend
          pip install -r requirements.txt

      # ⚠️ 注意: README 中未提及测试命令，此步骤为示例
      - name: Run Backend tests (Placeholder)
        run: echo "No explicit test command found. Skipping."