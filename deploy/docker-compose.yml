version: '3.9'
services:
  frontend:
    image: nginx:alpine
    container_name: findu-frontend-static
    restart: unless-stopped
    ports:
      - "8001:80"
    volumes:
      - ./out:/usr/share/nginx/html:ro

  backend:
    build:
      context: ./findu-backend
      dockerfile: Dockerfile
    container_name: findu-backend-api
    restart: unless-stopped
    environment:
      - HOST=0.0.0.0
      - PORT=8000
      - JWT_SECRET=${JWT_SECRET:-change_me}
      - STORAGE_PATH=static/demands
      - STORAGE_URL=http://127.0.0.1:8002
    ports:
      - "8002:8000"
    volumes:
      - backend_storage:/app/static

volumes:
  backend_storage:

