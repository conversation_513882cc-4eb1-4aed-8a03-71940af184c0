# FindU 完整部署配置
services:
  # MongoDB 数据库
  mongodb:
    image: mongo:7.0
    container_name: findu-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - findu-network

  # 后端服务
  backend:
    build:
      context: ./findu-backend
      dockerfile: Dockerfile
    container_name: findu-backend
    restart: unless-stopped
    environment:
      - MONGODB_URL=*****************************************
      - AI_API_URL=https://api.x.ai/v1/chat/completions
      - X=${X}
      - FRONTEND_URL=http://localhost:3000
      - JWT_SECRET=${JWT_SECRET:-default_jwt_secret}
      - STORAGE_PATH=static/demands
      - STORAGE_URL=http://localhost:8000
    ports:
      - "8000:8000"
    depends_on:
      - mongodb
    volumes:
      - backend_storage:/app/static
    networks:
      - findu-network

  # 前端服务
  frontend:
    build:
      context: ./findu-frontend
      dockerfile: Dockerfile
    container_name: findu-frontend
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api
      - NEXT_PUBLIC_APP_NAME=AI需求生成器
      - NEXT_PUBLIC_DEFAULT_LOCALE=en
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - findu-network

volumes:
  mongodb_data:
  backend_storage:

networks:
  findu-network:
    driver: bridge
